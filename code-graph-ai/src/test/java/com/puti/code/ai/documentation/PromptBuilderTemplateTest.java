package com.puti.code.ai.documentation;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试统一批次分析模板的功能
 */
class PromptBuilderTemplateTest {

    @Test
    void testUnifiedBatchAnalysisTemplate() {
        // 测试统一模板是否存在并可以正确渲染
        Resource templateResource = new ClassPathResource("/prompts/batch-analysis.st");
        assertTrue(templateResource.exists(), "统一批次分析模板文件应该存在");

        PromptTemplate template = new PromptTemplate(templateResource);
        
        // 测试第1层
        Map<String, Object> variables = createTestVariables(1);
        String result1 = template.render(variables);
        assertNotNull(result1);
        assertTrue(result1.contains("第1层核心流程分析"));
        assertTrue(result1.contains("核心流程和关键调用路径"));
        
        // 测试第2层
        variables = createTestVariables(2);
        variables.put("previousDocumentationSummary", "## 上一层分析总结\n这是第1层的分析结果");
        String result2 = template.render(variables);
        assertNotNull(result2);
        assertTrue(result2.contains("第2层详细流程分析"));
        assertTrue(result2.contains("详细实现、技术特点"));
        assertTrue(result2.contains("上一层分析总结"));
        
        // 测试第3层
        variables = createTestVariables(3);
        variables.put("previousDocumentationSummary", "## 上一层分析总结\n这是第2层的分析结果");
        String result3 = template.render(variables);
        assertNotNull(result3);
        assertTrue(result3.contains("第3层完整系统分析"));
        assertTrue(result3.contains("完整调用链路和系统架构"));
        assertTrue(result3.contains("上一层分析总结"));
    }

    private Map<String, Object> createTestVariables(int level) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("level", level);
        variables.put("levelDescription", getLevelDescription(level));
        variables.put("batchIndex", 1);
        variables.put("totalBatches", 2);
        variables.put("entryPoint", "com.example.TestService.testMethod");
        variables.put("codeContent", "// 测试代码内容");
        variables.put("analysisRequirements", "测试分析要求");
        variables.put("documentStructure", "测试文档结构");
        return variables;
    }

    private String getLevelDescription(int level) {
        return switch (level) {
            case 1 -> "核心流程";
            case 2 -> "详细流程";
            default -> "完整系统";
        };
    }
}
